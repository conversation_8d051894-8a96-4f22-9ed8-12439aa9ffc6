@echo off
REM 驗證版FTP下載腳本 - 基於手動測試成功的命令序列
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo ==========================================
echo FTP文件下載腳本
echo 服務器: 139.196.19.5
echo 用戶: i-display
echo 目標目錄: C:\Content
echo ==========================================

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content...
    mkdir "C:\Content"
    if not exist "C:\Content" (
        echo 錯誤：無法創建目標目錄！
        pause
        exit /b 1
    )
)

REM 創建FTP命令文件
echo 準備FTP命令...
(
echo 139.196.19.5
echo i-display
echo roctec
echo cd /On/Content/
echo dir
echo binary
echo lcd C:\Content
echo prompt off
echo hash on
echo get 302352_20250901165414F.jpg
echo get 302351_20250911165124F.jpg
echo quit
) > ftp_commands.txt

REM 顯示即將執行的命令
echo.
echo 即將執行以下FTP命令：
echo ------------------------------------------
type ftp_commands.txt
echo ------------------------------------------
echo.

REM 執行FTP命令
echo 開始FTP下載...
ftp -v -s:ftp_commands.txt

echo.
echo ==========================================
echo 下載完成，檢查結果：
echo ==========================================

REM 詳細檢查下載結果
set /a success_count=0

if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 下載成功
    for %%A in ("C:\Content\302352_20250901165414F.jpg") do (
        echo   文件大小: %%~zA 字節
        echo   修改時間: %%~tA
    )
    set /a success_count+=1
) else (
    echo ✗ 302352_20250901165414F.jpg 下載失敗
)

echo.
if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 下載成功
    for %%A in ("C:\Content\302351_20250911165124F.jpg") do (
        echo   文件大小: %%~zA 字節
        echo   修改時間: %%~tA
    )
    set /a success_count+=1
) else (
    echo ✗ 302351_20250911165124F.jpg 下載失敗
)

echo.
echo ==========================================
echo 總結: %success_count%/2 個文件下載成功
echo ==========================================

REM 清理臨時文件
if exist ftp_commands.txt (
    del ftp_commands.txt
    echo 臨時文件已清理。
)

echo.
echo 按任意鍵退出...
pause > nul
