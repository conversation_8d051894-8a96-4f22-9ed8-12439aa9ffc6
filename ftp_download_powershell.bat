@echo off
REM PowerShell版FTP下載腳本
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo 開始PowerShell FTP下載程序...

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content
    mkdir "C:\Content"
)

REM 使用PowerShell執行FTP下載
powershell -Command "& {
    $ftpServer = 'ftp://************'
    $username = 'i-display'
    $password = 'roctec'
    $remotePath1 = '/On/Content/302352_20250901165414F.jpg'
    $remotePath2 = '/On/Content/302351_20250911165124F.jpg'
    $localPath = 'C:\Content\'
    
    # 創建WebClient對象
    $webclient = New-Object System.Net.WebClient
    $webclient.Credentials = New-Object System.Net.NetworkCredential($username, $password)
    
    try {
        Write-Host '正在下載第一個文件...'
        $webclient.DownloadFile($ftpServer + $remotePath1, $localPath + '302352_20250901165414F.jpg')
        Write-Host '第一個文件下載完成'
        
        Write-Host '正在下載第二個文件...'
        $webclient.DownloadFile($ftpServer + $remotePath2, $localPath + '302351_20250911165124F.jpg')
        Write-Host '第二個文件下載完成'
        
        Write-Host '所有文件下載成功！'
    }
    catch {
        Write-Host '下載失敗：' $_.Exception.Message
    }
    finally {
        $webclient.Dispose()
    }
}"

REM 檢查文件是否實際下載成功
echo.
echo 檢查下載的文件：
if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 下載成功
) else (
    echo ✗ 302352_20250901165414F.jpg 下載失敗
)

if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 下載成功
) else (
    echo ✗ 302351_20250911165124F.jpg 下載失敗
)

echo.
echo 按任意鍵退出...
pause > nul
