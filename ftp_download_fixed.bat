@echo off
REM 修復版FTP下載腳本
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo 開始FTP下載程序...

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content
    mkdir "C:\Content"
)

REM 設置FTP服務器詳細信息
set FTP_SERVER=139.196.19.5
set FTP_USER=i-display
set FTP_PASS=roctec
set FTP_REMOTE_DIR=/On/Content/
set LOCAL_DIR=C:\Content

REM 創建臨時FTP腳本文件，使用匿名登錄格式
echo 創建FTP命令腳本...
echo open %FTP_SERVER% > ftp_commands.txt
echo %FTP_USER% >> ftp_commands.txt
echo %FTP_PASS% >> ftp_commands.txt
echo ascii >> ftp_commands.txt
echo cd %FTP_REMOTE_DIR% >> ftp_commands.txt
echo binary >> ftp_commands.txt
echo lcd %LOCAL_DIR% >> ftp_commands.txt
echo prompt off >> ftp_commands.txt
echo hash on >> ftp_commands.txt
echo get 302352_20250901165414F.jpg >> ftp_commands.txt
echo get 302351_20250911165124F.jpg >> ftp_commands.txt
echo quit >> ftp_commands.txt

REM 執行FTP命令
echo 連接到FTP服務器並下載文件...
ftp -v -s:ftp_commands.txt

REM 檢查下載是否成功
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo FTP下載完成！
    echo 文件已下載到: %LOCAL_DIR%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 錯誤：FTP下載失敗！
    echo 錯誤代碼: %errorlevel%
    echo ========================================
)

REM 清理臨時文件
if exist ftp_commands.txt (
    del ftp_commands.txt
    echo 臨時FTP腳本文件已清理。
)

REM 檢查文件是否實際下載成功
echo.
echo 檢查下載的文件：
if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 下載成功
) else (
    echo ✗ 302352_20250901165414F.jpg 下載失敗
)

if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 下載成功
) else (
    echo ✗ 302351_20250911165124F.jpg 下載失敗
)

echo.
echo 按任意鍵退出...
pause > nul
