@echo off
REM 簡化版FTP下載腳本 - 模擬手動輸入
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo 開始FTP下載程序...

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content
    mkdir "C:\Content"
)

REM 創建FTP命令文件 - 完全按照手動輸入的順序
echo 創建FTP命令腳本...
echo 139.196.19.5 > ftp_commands.txt
echo i-display >> ftp_commands.txt
echo roctec >> ftp_commands.txt
echo cd /On/Content/ >> ftp_commands.txt
echo binary >> ftp_commands.txt
echo lcd C:\Content >> ftp_commands.txt
echo prompt off >> ftp_commands.txt
echo get 302352_20250901165414F.jpg >> ftp_commands.txt
echo get 302351_20250911165124F.jpg >> ftp_commands.txt
echo quit >> ftp_commands.txt

REM 執行FTP命令
echo 連接到FTP服務器並下載文件...
ftp -s:ftp_commands.txt

REM 檢查下載結果
echo.
echo ========================================
echo 檢查下載的文件：
if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 下載成功
    for %%A in ("C:\Content\302352_20250901165414F.jpg") do echo   文件大小: %%~zA 字節
) else (
    echo ✗ 302352_20250901165414F.jpg 下載失敗
)

if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 下載成功
    for %%A in ("C:\Content\302351_20250911165124F.jpg") do echo   文件大小: %%~zA 字節
) else (
    echo ✗ 302351_20250911165124F.jpg 下載失敗
)
echo ========================================

REM 清理臨時文件
if exist ftp_commands.txt (
    del ftp_commands.txt
    echo 臨時FTP腳本文件已清理。
)

echo.
echo 按任意鍵退出...
pause > nul
