@echo off
REM CURL版FTP下載腳本 - 更可靠的下載方法
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo ==========================================
echo CURL FTP文件下載腳本
echo 服務器: 139.196.19.5
echo 用戶: i-display
echo ==========================================

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content...
    mkdir "C:\Content"
)

REM 檢查CURL是否可用
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤：系統中未找到CURL命令
    echo 請安裝CURL或使用其他FTP腳本
    pause
    exit /b 1
)

echo CURL可用，開始下載...

REM 設置FTP參數
set FTP_URL=ftp://139.196.19.5/On/Content/
set FTP_USER=i-display:roctec
set OUTPUT_DIR=C:\Content\

echo.
echo 下載第一個文件: 302352_20250901165414F.jpg
curl -u %FTP_USER% --ftp-pasv -o "%OUTPUT_DIR%302352_20250901165414F.jpg" "%FTP_URL%302352_20250901165414F.jpg"

if %errorlevel% equ 0 (
    echo ✓ 第一個文件下載成功
) else (
    echo ✗ 第一個文件下載失敗
)

echo.
echo 下載第二個文件: 302351_20250911165124F.jpg
curl -u %FTP_USER% --ftp-pasv -o "%OUTPUT_DIR%302351_20250911165124F.jpg" "%FTP_URL%302351_20250911165124F.jpg"

if %errorlevel% equ 0 (
    echo ✓ 第二個文件下載成功
) else (
    echo ✗ 第二個文件下載失敗
)

echo.
echo ==========================================
echo 最終結果檢查：
echo ==========================================

set /a success_count=0

if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 存在
    for %%A in ("C:\Content\302352_20250901165414F.jpg") do echo   文件大小: %%~zA 字節
    set /a success_count+=1
) else (
    echo ✗ 302352_20250901165414F.jpg 不存在
)

if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 存在
    for %%A in ("C:\Content\302351_20250911165124F.jpg") do echo   文件大小: %%~zA 字節
    set /a success_count+=1
) else (
    echo ✗ 302351_20250911165124F.jpg 不存在
)

echo.
echo 總計: %success_count%/2 個文件下載成功
echo ==========================================

echo.
echo 按任意鍵退出...
pause > nul
