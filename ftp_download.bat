@echo off
REM FTP Download Script
REM Downloads files from FTP server /On/Content/ to C:\Content

echo Starting FTP download process...

REM Create destination directory if it doesn't exist
if not exist "C:\Content" (
    echo Creating destination directory C:\Content
    mkdir "C:\Content"
)

REM Set FTP server details (modify these variables as needed)
set FTP_SERVER=************
set FTP_USER=i-display
set FTP_PASS=roctec
set FTP_REMOTE_DIR=/On/Content/
set LOCAL_DIR=C:\Content

REM Create temporary FTP script file
echo Creating FTP command script...
echo open %FTP_SERVER% > ftp_commands.txt
echo %FTP_USER% >> ftp_commands.txt
echo %FTP_PASS% >> ftp_commands.txt
echo binary >> ftp_commands.txt
echo cd %FTP_REMOTE_DIR% >> ftp_commands.txt
echo lcd %LOCAL_DIR% >> ftp_commands.txt
echo prompt off >> ftp_commands.txt

REM Add your specific files here - replace "file1.txt" and "file2.txt" with actual filenames
echo get 302352_20250901165414F.jpg >> ftp_commands.txt
echo get 302351_20250911165124F.jpg >> ftp_commands.txt

echo quit >> ftp_commands.txt

REM Execute FTP commands
echo Connecting to FTP server and downloading files...
ftp -s:ftp_commands.txt

REM Check if download was successful
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo FTP download completed successfully!
    echo Files downloaded to: %LOCAL_DIR%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Error: FTP download failed!
    echo Error level: %errorlevel%
    echo ========================================
)

REM Clean up temporary file
if exist ftp_commands.txt (
    del ftp_commands.txt
    echo Temporary FTP script file cleaned up.
)

echo.
echo Press any key to exit...
pause > nul
