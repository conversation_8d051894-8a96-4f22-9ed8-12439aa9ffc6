@echo off
REM 工作版FTP下載腳本 - 確保連接成功
REM 從FTP服務器 /On/Content/ 下載文件到 C:\Content

echo ==========================================
echo FTP文件下載腳本
echo 服務器: 139.196.19.5
echo 用戶: i-display
echo ==========================================

REM 如果目標目錄不存在則創建
if not exist "C:\Content" (
    echo 創建目標目錄 C:\Content...
    mkdir "C:\Content"
)

REM 方法1: 使用標準FTP腳本
echo 創建FTP命令腳本...
(
echo open 139.196.19.5
echo user i-display roctec
echo cd /On/Content/
echo binary
echo lcd C:\Content
echo prompt off
echo hash on
echo get 302352_20250901165414F.jpg
echo get 302351_20250911165124F.jpg
echo quit
) > ftp_commands.txt

echo 開始FTP下載（方法1）...
ftp -v -s:ftp_commands.txt

REM 檢查是否下載成功
set /a downloaded=0
if exist "C:\Content\302352_20250901165414F.jpg" set /a downloaded+=1
if exist "C:\Content\302351_20250911165124F.jpg" set /a downloaded+=1

if %downloaded% LSS 2 (
    echo.
    echo 方法1失敗，嘗試方法2...
    
    REM 方法2: 使用分離的用戶名和密碼
    (
    echo open 139.196.19.5
    echo i-display
    echo roctec
    echo cd /On/Content/
    echo binary
    echo lcd C:\Content
    echo prompt off
    echo get 302352_20250901165414F.jpg
    echo get 302351_20250911165124F.jpg
    echo quit
    ) > ftp_commands2.txt
    
    ftp -v -s:ftp_commands2.txt
    
    REM 重新檢查
    set /a downloaded=0
    if exist "C:\Content\302352_20250901165414F.jpg" set /a downloaded+=1
    if exist "C:\Content\302351_20250911165124F.jpg" set /a downloaded+=1
)

echo.
echo ==========================================
echo 下載結果檢查：
echo ==========================================

if exist "C:\Content\302352_20250901165414F.jpg" (
    echo ✓ 302352_20250901165414F.jpg 下載成功
    for %%A in ("C:\Content\302352_20250901165414F.jpg") do echo   文件大小: %%~zA 字節
) else (
    echo ✗ 302352_20250901165414F.jpg 下載失敗
)

if exist "C:\Content\302351_20250911165124F.jpg" (
    echo ✓ 302351_20250911165124F.jpg 下載成功
    for %%A in ("C:\Content\302351_20250911165124F.jpg") do echo   文件大小: %%~zA 字節
) else (
    echo ✗ 302351_20250911165124F.jpg 下載失敗
)

echo.
echo 總計: %downloaded%/2 個文件下載成功

REM 清理臨時文件
if exist ftp_commands.txt del ftp_commands.txt
if exist ftp_commands2.txt del ftp_commands2.txt

echo.
echo 按任意鍵退出...
pause > nul
